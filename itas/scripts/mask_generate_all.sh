#!/bin/bash

# Multi-GPU MASK Response Generation Script - Custom Layers Version
# Runs mask_response_generator.py for specified layers across specified GPUs

# Don't exit on errors - we want to continue processing other layers
# set -e  # Exit on any error

ALPHA="$1"
STEERING_DIRECTION="$2" # "truthful" or "scheming"
LAYERS_ARG="$3"

# Error handling for missing arguments
if [[ -z "$ALPHA" ]]; then
    echo "Usage: $0 <alpha> [layers]"
    echo "Example: $0 1.5"
    echo "Example: $0 1.5 \"6,7,9,12,13,19,20,24,27\""
    echo ""
    echo "If layers are not specified, will process all available layers"
    exit 1
fi

# Configuration - EDIT THESE VALUES
AVAILABLE_GPUS=(0 1 2 3 4 5 6 7)  # Specify which GPUs to use
if [[ -z "$STEERING_DIRECTION" ]]; then
    STEERING_DIRECTION="scheming"
    echo "Steering direction not specified, using default: $STEERING_DIRECTION"
else [[ "$STEERING_DIRECTION" != "truthful" && "$STEERING_DIRECTION" != "scheming" ]];
    echo "Invalid steering direction: $STEERING_DIRECTION"
    echo "Must be 'truthful' or 'scheming'"
    exit 1
fi

# Parse custom layers if provided
if [[ -n "$LAYERS_ARG" ]]; then
    # Convert comma-separated string to array
    IFS=',' read -ra CUSTOM_LAYERS <<< "$LAYERS_ARG"
    echo "Using custom layers: ${CUSTOM_LAYERS[*]}"
    USE_CUSTOM_LAYERS=true
else
    echo "No custom layers specified, will scan for all available layers"
    USE_CUSTOM_LAYERS=false
fi

MODEL_NAME="meta-llama/Llama-3.1-8B-Instruct"
RESULTS_DIR="/data_x/junkim100/projects/scheming_sae/itas/results/squad_v2/$(echo "$MODEL_NAME" | sed 's/\//-/g')_alpha$ALPHA/activations/"
# MASK_CSV_DIR="/data_x/junkim100/projects/scheming_sae/itas/data/mask/test/responses/"
MASK_CSV_DIR="/data_x/junkim100/projects/scheming_sae/mask_benchmark/mask/csv_data/original/llama-3.1-8b_responses"
OUTPUT_DIR="/data_x/junkim100/projects/scheming_sae/itas/results/squad_v2/$(echo "$MODEL_NAME" | sed 's/\//-/g')_alpha$ALPHA/mask_responses_$STEERING_DIRECTION/"

NUM_GPUS=${#AVAILABLE_GPUS[@]}

# MASK CSV files to process - MODIFY THESE TO MATCH YOUR ACTUAL FILENAMES
# For testing, process 2 categories
MASK_FILES=(
    "continuations"
    "disinformation"
    "doubling_down_known_facts"
    "known_facts"
    "provided_facts"
    "statistics"
)

# Simple approach: launch all tasks in batches
declare -a batch_pids=()
declare -a batch_task_info=()

# Track successful and failed tasks
declare -a successful_tasks=()
declare -a failed_tasks=()

# Track background evaluation processes
declare -a eval_pids=()
declare -a eval_layers=()

# Track all running batch processes globally for monitoring
declare -a batch_pids=()

# Track GPU usage to prevent conflicts
declare -A gpu_in_use=()

# Function to find activation/config pairs for a specific layer
find_layer_files() {
    local layer=$1

    # Look for files matching the pattern: *_L{layer}_*
    local activation_file=$(find "$RESULTS_DIR" -name "*_L${layer}_*.pt" | head -1)
    local config_file=$(find "$RESULTS_DIR" -name "*_L${layer}_*.json" | head -1)

    if [[ -n "$activation_file" && -n "$config_file" ]]; then
        echo "$activation_file|$config_file"
    else
        echo ""
    fi
}

# Function to wait for all processes in current batch
wait_for_batch() {
    echo "Waiting for current batch to complete..."
    for i in "${!batch_pids[@]}"; do
        local pid=${batch_pids[i]}
        local task_info=${batch_task_info[i]}

        if kill -0 "$pid" 2>/dev/null; then
            wait "$pid"
            local exit_code=$?
            if [[ $exit_code -eq 0 ]]; then
                echo "✅ Process $pid completed successfully: $task_info"
                successful_tasks+=("$task_info")
            else
                echo "❌ Process $pid failed with exit code $exit_code: $task_info"
                failed_tasks+=("$task_info")
            fi
        else
            echo "⚠️  Process $pid was not running: $task_info"
            failed_tasks+=("$task_info")
        fi
    done
    batch_pids=()  # Clear the batch
    batch_task_info=()  # Clear task info
}

# Function to run evaluation for a completed layer in the background
run_layer_evaluation_background() {
    local layer=$1
    local layer_dir="${OUTPUT_DIR}layer_${layer}/"

    echo ""
    echo "=== Starting background evaluation for layer $layer ==="
    echo "Layer directory: $layer_dir"

    # Check if the layer directory exists and has CSV files
    if [[ ! -d "$layer_dir" ]]; then
        echo "❌ Layer directory does not exist: $layer_dir"
        return 1
    fi

    local csv_count=$(find "$layer_dir" -name "*.csv" | wc -l)
    if [[ $csv_count -eq 0 ]]; then
        echo "❌ No CSV files found in layer directory: $layer_dir"
        return 1
    fi

    echo "Found $csv_count CSV files in layer directory"

    # Create evaluation log file
    local eval_log="${layer_dir}evaluation.log"

    # Run the evaluation script in background
    (
        echo "Starting evaluation for layer $layer at $(date)"
        if bash mask_eval.sh "$layer_dir"; then
            echo "✅ Evaluation completed successfully for layer $layer at $(date)"
            # Create a success marker file
            touch "${layer_dir}evaluation_success.marker"
        else
            echo "❌ Evaluation failed for layer $layer at $(date)"
            # Create a failure marker file
            touch "${layer_dir}evaluation_failed.marker"
        fi
    ) > "$eval_log" 2>&1 &

    local eval_pid=$!
    echo "🔄 Evaluation started in background with PID $eval_pid"
    echo "📝 Evaluation log: $eval_log"

    # Store the PID for tracking
    eval_pids+=($eval_pid)
    eval_layers+=($layer)

    return 0
}

# Function to check if a layer has all 6 CSV files completed
check_layer_completion() {
    local layer=$1
    local layer_dir="${OUTPUT_DIR}layer_${layer}/"

    # Check if directory exists
    if [[ ! -d "$layer_dir" ]]; then
        return 1
    fi

    # Count CSV files in the layer directory
    local csv_count=$(find "$layer_dir" -name "*.csv" -type f | wc -l)

    # Check if we have all 6 CSV files
    if [[ $csv_count -eq 6 ]]; then
        return 0  # Layer is complete
    else
        return 1  # Layer is not complete
    fi
}

# Function to check if evaluation has already been started for a layer
evaluation_already_started() {
    local layer=$1
    local layer_dir="${OUTPUT_DIR}layer_${layer}/"

    # Check if evaluation log exists or marker files exist
    if [[ -f "${layer_dir}evaluation.log" ]] || \
       [[ -f "${layer_dir}evaluation_success.marker" ]] || \
       [[ -f "${layer_dir}evaluation_failed.marker" ]]; then
        return 0  # Evaluation already started
    else
        return 1  # Evaluation not started
    fi
}

# Function to monitor and start evaluations for completed layers
monitor_and_evaluate_layers() {
    echo "🔍 Monitoring layers for completion and starting evaluations..."

    # Get the list of layers to monitor
    local layers_to_monitor=()
    if [[ "$USE_CUSTOM_LAYERS" == true ]]; then
        layers_to_monitor=("${CUSTOM_LAYERS[@]}")
        echo "Monitoring custom layers: ${layers_to_monitor[*]}"
    else
        layers_to_monitor=($(seq 0 31))
        echo "Monitoring all layers: 0-31"
    fi

    while true; do
        local found_new_completion=false

        # Check each layer in our monitoring list
        for layer in "${layers_to_monitor[@]}"; do
            # Skip if evaluation already started for this layer
            if evaluation_already_started $layer; then
                continue
            fi

            # Check if this layer is complete
            if check_layer_completion $layer; then
                echo ""
                echo "🎉 Layer $layer completed! Starting background evaluation..."
                run_layer_evaluation_background $layer
                found_new_completion=true
            fi
        done

        # If no new completions found, sleep for a bit before checking again
        if [[ "$found_new_completion" == false ]]; then
            sleep 10
        fi

        # Check if all tasks are done (no more running processes)
        local running_tasks=0
        for pid in "${batch_pids[@]}"; do
            if kill -0 "$pid" 2>/dev/null; then
                running_tasks=$((running_tasks + 1))
            fi
        done

        # If no tasks are running, break the monitoring loop
        if [[ $running_tasks -eq 0 ]]; then
            echo "🏁 All generation tasks completed, stopping layer monitoring"
            break
        fi
    done
}

# Function to wait for all background evaluations to complete
wait_for_all_evaluations() {
    if [[ ${#eval_pids[@]} -eq 0 ]]; then
        echo "No background evaluations to wait for."
        return 0
    fi

    echo ""
    echo "=========================================="
    echo "=== Waiting for background evaluations to complete ==="
    echo "=========================================="
    echo "Waiting for ${#eval_pids[@]} evaluation processes..."

    for i in "${!eval_pids[@]}"; do
        local pid=${eval_pids[i]}
        local layer=${eval_layers[i]}

        echo "Waiting for evaluation of layer $layer (PID: $pid)..."

        if kill -0 "$pid" 2>/dev/null; then
            wait "$pid"
            local exit_code=$?

            # Check for marker files to determine success/failure
            local layer_dir="${OUTPUT_DIR}layer_${layer}/"
            if [[ -f "${layer_dir}evaluation_success.marker" ]]; then
                echo "✅ Evaluation completed successfully for layer $layer"
            elif [[ -f "${layer_dir}evaluation_failed.marker" ]]; then
                echo "❌ Evaluation failed for layer $layer"
            else
                echo "⚠️  Evaluation status unknown for layer $layer"
            fi
        else
            echo "⚠️  Evaluation process for layer $layer was not running"
        fi
    done

    echo "=== All background evaluations completed ==="
}

# Function to get available layers
get_available_layers() {
    local available_layers=()

    if [[ "$USE_CUSTOM_LAYERS" == true ]]; then
        echo "Using custom layers: ${CUSTOM_LAYERS[*]}" >&2

        # Validate that each custom layer has files
        for layer in "${CUSTOM_LAYERS[@]}"; do
            local files=$(find_layer_files $layer)
            if [[ -n "$files" ]]; then
                available_layers+=($layer)
                echo "  Found layer $layer" >&2
            else
                echo "  Layer $layer: No files found (skipping)" >&2
            fi
        done
    else
        echo "Scanning for all available layers (0-31)..." >&2
        for ((layer=0; layer<32; layer++)); do
            local files=$(find_layer_files $layer)
            if [[ -n "$files" ]]; then
                available_layers+=($layer)
                echo "  Found layer $layer" >&2
            else
                echo "  Layer $layer: No files found" >&2
            fi
        done
    fi

    echo "${available_layers[@]}"
}

# Main execution
echo "Starting multi-GPU MASK response generation..."
if [[ "$USE_CUSTOM_LAYERS" == true ]]; then
    echo "Custom Layers: ${CUSTOM_LAYERS[*]}"
else
    echo "Mode: All available layers"
fi
echo "Steering Direction: $STEERING_DIRECTION"
echo "Available GPUs: ${AVAILABLE_GPUS[*]} (${NUM_GPUS} total)"
echo "Results Dir: $RESULTS_DIR"
echo "MASK CSV Dir: $MASK_CSV_DIR"
echo "Output Dir: $OUTPUT_DIR"
echo ""

# Create output directory
mkdir -p "$OUTPUT_DIR"

# Check if results directory exists and has files
echo "Checking results directory..."
if [[ ! -d "$RESULTS_DIR" ]]; then
    echo "ERROR: Results directory does not exist: $RESULTS_DIR"
    # Create the directory
    mkdir -p "$RESULTS_DIR"
fi

activation_count=$(find "$RESULTS_DIR" -name "*.pt" | wc -l)
config_count=$(find "$RESULTS_DIR" -name "*.json" | wc -l)
echo "Found $activation_count .pt files and $config_count .json files in results directory"

if [[ $activation_count -eq 0 ]]; then
    echo "ERROR: No activation files (.pt) found in $RESULTS_DIR"
    echo "Please run the activation generation script first:"
    echo "  cd /data_x/junkim100/projects/scheming_sae/itas/scripts"
    echo "  ./run_all_layers.sh"
    exit 1
fi

# Get available layers for the specified
available_layers=($(get_available_layers))

if [[ ${#available_layers[@]} -eq 0 ]]; then
    echo "ERROR: No activation/config files found"
    echo "Available files:"
    find "$RESULTS_DIR" -name "*.pt" | head -5
    exit 1
fi

echo "Found ${#available_layers[@]} layers: ${available_layers[*]}"
echo "Will process ${#MASK_FILES[@]} CSV files per layer"
echo "Total tasks: $((${#available_layers[@]} * ${#MASK_FILES[@]}))"
echo ""

# Alternative approach: Process tasks in batches to ensure proper GPU management
process_tasks_in_batches() {
    local all_tasks=()

    # Build list of all tasks
    for layer in "${available_layers[@]}"; do
        # Find the activation and config files for this layer
        files=$(find_layer_files $layer)
        if [[ -z "$files" ]]; then
            echo "WARNING: No files found for layer $layer"
            continue
        fi

        activation_file=$(echo "$files" | cut -d'|' -f1)
        config_file=$(echo "$files" | cut -d'|' -f2)

        # Create layer-specific output directory
        mkdir -p "${OUTPUT_DIR}layer_${layer}/"

        for csv_file in "${MASK_FILES[@]}"; do
            all_tasks+=("$layer|$csv_file|$activation_file|$config_file")
        done
    done

    echo "Total tasks to process: ${#all_tasks[@]}"

    # Process tasks in batches of NUM_GPUS
    local task_index=0
    while [[ $task_index -lt ${#all_tasks[@]} ]]; do
        echo ""
        echo "=========================================="
        echo "=== Processing batch starting at task $((task_index + 1)) ==="
        echo "=========================================="

        # Start up to NUM_GPUS tasks in parallel
        local batch_task_info=()
        local batch_gpu_assignments=()
        local current_batch_pids=()

        for ((gpu_idx=0; gpu_idx<$NUM_GPUS && task_index<${#all_tasks[@]}; gpu_idx++)); do
            local task_data="${all_tasks[$task_index]}"
            local layer=$(echo "$task_data" | cut -d'|' -f1)
            local csv_file=$(echo "$task_data" | cut -d'|' -f2)
            local activation_file=$(echo "$task_data" | cut -d'|' -f3)
            local config_file=$(echo "$task_data" | cut -d'|' -f4)
            local gpu_id=${AVAILABLE_GPUS[gpu_idx]}

            echo ""
            echo "--- Starting task $((task_index + 1)): layer $layer, CSV $csv_file, GPU $gpu_id ---"

            # Find the CSV file
            actual_csv_file=$(find "${MASK_CSV_DIR}" -type f -name "${csv_file}_*.csv" | head -1)
            if [[ -z "$actual_csv_file" ]]; then
                echo "ERROR: No CSV file found matching pattern '${csv_file}' in ${MASK_CSV_DIR}"
                task_index=$((task_index + 1))
                continue
            fi

            csv_name=$(basename "$csv_file" .csv)
            log_file="${OUTPUT_DIR}layer_${layer}/log_${csv_name}_gpu${gpu_id}.log"

            echo "Using CSV file: $actual_csv_file"
            echo "Log file: $log_file"

            # Start the task
            echo "Executing: CUDA_VISIBLE_DEVICES=$gpu_id python mask_response_generator.py ..."
            CUDA_VISIBLE_DEVICES=$gpu_id python mask_response_generator.py \
                --input_csv="$actual_csv_file" \
                --config_path="$config_file" \
                --activations_path="$activation_file" \
                --steering_direction="$STEERING_DIRECTION" \
                --output_dir="${OUTPUT_DIR}layer_${layer}/" \
                > "$log_file" 2>&1 &

            task_pid=$!
            batch_pids+=($task_pid)  # Add to global array for monitoring
            current_batch_pids+=($task_pid)  # Add to local array for batch waiting
            batch_task_info+=("Layer $layer, CSV $csv_file, GPU $gpu_id")
            batch_gpu_assignments+=($gpu_id)

            echo "Task started with PID: $task_pid"
            task_index=$((task_index + 1))

            # Small delay between starting processes
            sleep 2
        done

        # Wait for all tasks in this batch to complete
        echo ""
        echo "=== Waiting for batch to complete (${#current_batch_pids[@]} tasks) ==="

        for i in "${!current_batch_pids[@]}"; do
            local pid=${current_batch_pids[i]}
            local task_info=${batch_task_info[i]}

            if kill -0 "$pid" 2>/dev/null; then
                wait "$pid"
                local exit_code=$?
                if [[ $exit_code -eq 0 ]]; then
                    echo "✅ Process $pid completed successfully: $task_info"
                    successful_tasks+=("$task_info")
                else
                    echo "❌ Process $pid failed with exit code $exit_code: $task_info"
                    failed_tasks+=("$task_info")
                fi
            else
                echo "⚠️  Process $pid was not running: $task_info"
                failed_tasks+=("$task_info")
            fi
        done

        echo "Batch completed. Released GPUs: ${batch_gpu_assignments[*]}"
    done
}

# Process each layer completely before moving to the next (ORIGINAL APPROACH - COMMENTED OUT)
# Keeping this for reference but using the batch approach instead
process_layers_sequentially() {
    local task_count=0
    local layer_count=0

    for layer in "${available_layers[@]}"; do
        echo ""
        echo "=========================================="
        echo "=== Processing layer $layer ($(($layer_count + 1))/${#available_layers[@]}) ==="
        echo "=========================================="

        # Find the activation and config files for this layer
        files=$(find_layer_files $layer)
        if [[ -z "$files" ]]; then
            echo "WARNING: No files found for layer $layer"
            continue
        fi

        activation_file=$(echo "$files" | cut -d'|' -f1)
        config_file=$(echo "$files" | cut -d'|' -f2)

        echo "Layer $layer files:"
        echo "  Activation: $activation_file"
        echo "  Config: $config_file"

        # Create layer-specific output directory
        mkdir -p "${OUTPUT_DIR}layer_${layer}/"
        echo "  Created directory: ${OUTPUT_DIR}layer_${layer}/"

        # Process all CSV files for this layer in parallel
        layer_pids=()
        layer_task_info=()
        layer_gpu_assignments=()

        for csv_file in "${MASK_FILES[@]}"; do
            echo ""
            echo "--- Starting layer $layer, CSV $csv_file ---"

            # Get GPU for this task (cycle through available GPUs)
            gpu_index=$((${#layer_pids[@]} % $NUM_GPUS))
            gpu_id=${AVAILABLE_GPUS[gpu_index]}

            # Mark GPU as in use for this layer
            gpu_in_use[$gpu_id]=1
            layer_gpu_assignments+=($gpu_id)

            echo "Starting task: layer $layer, CSV $csv_file, GPU $gpu_id"
            echo "GPU usage status: ${!gpu_in_use[*]}"

            # Find the CSV file
            actual_csv_file=$(find "${MASK_CSV_DIR}" -type f -name "${csv_file}_*.csv" | head -1)
            if [[ -z "$actual_csv_file" ]]; then
                echo "ERROR: No CSV file found matching pattern '${csv_file}' in ${MASK_CSV_DIR}"
                continue
            fi

            csv_name=$(basename "$csv_file" .csv)
            log_file="${OUTPUT_DIR}layer_${layer}/log_${csv_name}_gpu${gpu_id}.log"

            echo "Using CSV file: $actual_csv_file"
            echo "Log file: $log_file"

            # Check if GPU is already in use (safety check)
            if [[ -n "${gpu_in_use[$gpu_id]}" ]] && [[ "${gpu_in_use[$gpu_id]}" != "1" ]]; then
                echo "WARNING: GPU $gpu_id appears to be in use by another process"
            fi

            # Start the task
            echo "Executing: CUDA_VISIBLE_DEVICES=$gpu_id python mask_response_generator.py ..."
            CUDA_VISIBLE_DEVICES=$gpu_id python mask_response_generator.py \
                --input_csv="$actual_csv_file" \
                --config_path="$config_file" \
                --activations_path="$activation_file" \
                --steering_direction="$STEERING_DIRECTION" \
                --output_dir="${OUTPUT_DIR}layer_${layer}/" \
                > "$log_file" 2>&1 &

            task_pid=$!
            layer_pids+=($task_pid)
            layer_task_info+=("Layer $layer, CSV $csv_file, GPU $gpu_id")

            echo "Task started with PID: $task_pid"
            task_count=$((task_count + 1))

            # Small delay between starting processes
            sleep 3
        done

        # Wait for all tasks in this layer to complete
        echo ""
        echo "=== Waiting for all tasks in layer $layer to complete ==="
        layer_success=true

        for i in "${!layer_pids[@]}"; do
            local pid=${layer_pids[i]}
            local task_info=${layer_task_info[i]}

            if kill -0 "$pid" 2>/dev/null; then
                wait "$pid"
                local exit_code=$?
                if [[ $exit_code -eq 0 ]]; then
                    echo "✅ Process $pid completed successfully: $task_info"
                    successful_tasks+=("$task_info")
                else
                    echo "❌ Process $pid failed with exit code $exit_code: $task_info"
                    failed_tasks+=("$task_info")
                    layer_success=false
                fi
            else
                echo "⚠️  Process $pid was not running: $task_info"
                failed_tasks+=("$task_info")
                layer_success=false
            fi
        done

        # Clear GPU usage tracking for this layer
        for gpu_id in "${layer_gpu_assignments[@]}"; do
            unset gpu_in_use[$gpu_id]
        done

        echo "Released GPUs for layer $layer: ${layer_gpu_assignments[*]}"

        # Start background evaluation for this layer if all tasks succeeded
        if [[ "$layer_success" == true ]]; then
            run_layer_evaluation_background $layer
            if [[ $? -ne 0 ]]; then
                echo "⚠️  Failed to start evaluation for layer $layer"
            fi
        else
            echo "⚠️  Skipping evaluation for layer $layer due to failed tasks"
        fi

        layer_count=$((layer_count + 1))
        echo "=== Layer $layer processing completed ==="
    done
}

# Choose processing approach
echo "Choose processing approach:"
echo "1. Batch processing (recommended - prevents GPU conflicts)"
echo "2. Layer-by-layer processing (original approach)"
echo ""
echo "Using batch processing approach to prevent GPU conflicts..."

# Start background monitoring for layer completions and evaluations
monitor_and_evaluate_layers &
monitor_pid=$!
echo "🔍 Started background layer monitoring (PID: $monitor_pid)"

# Use batch processing approach
process_tasks_in_batches

# Stop the background monitoring
if kill -0 "$monitor_pid" 2>/dev/null; then
    kill "$monitor_pid" 2>/dev/null
    echo "🛑 Stopped background layer monitoring"
fi

# Background monitoring will handle evaluations automatically as layers complete

# Wait for all background evaluations to complete
wait_for_all_evaluations

echo ""
echo "=========================================="
echo "All MASK response generation completed!"
echo "=========================================="
echo "📊 SUMMARY:"
echo "  Total tasks: $task_count"
echo "  ✅ Successful: ${#successful_tasks[@]}"
echo "  ❌ Failed: ${#failed_tasks[@]}"
echo ""

if [[ ${#successful_tasks[@]} -gt 0 ]]; then
    echo "✅ SUCCESSFUL TASKS:"
    for task in "${successful_tasks[@]}"; do
        echo "  - $task"
    done
    echo ""
fi

if [[ ${#failed_tasks[@]} -gt 0 ]]; then
    echo "❌ FAILED TASKS:"
    for task in "${failed_tasks[@]}"; do
        echo "  - $task"
    done
    echo ""
    echo "💡 Check the log files in the layer directories for error details."
    echo ""
fi

echo "Results saved in: $OUTPUT_DIR"
echo ""
echo "Directory structure:"
echo "  $OUTPUT_DIR"
for layer in "${available_layers[@]}"; do
    echo "    layer_${layer}/"
    for csv_file in "${MASK_FILES[@]}"; do
        csv_name=$(basename "$csv_file" .csv)
        echo "      ${csv_name}_*-${STEERING_DIRECTION}-steered.csv"
    done
done
